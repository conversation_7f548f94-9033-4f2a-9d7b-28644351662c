{% extends "base.html" %}

{% block title %}Test Agent - AI Agent System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Configuration
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Role Selection -->
                    <div class="mb-3">
                        <label for="roleSelect" class="form-label">Select Role</label>
                        <select class="form-select" id="roleSelect" onchange="loadRole()">
                            <option value="">Choose a role...</option>
                        </select>
                    </div>
                    
                    <!-- Role Info -->
                    <div id="roleInfo" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">Role Details</label>
                            <div class="card bg-light">
                                <div class="card-body p-2">
                                    <h6 id="roleDisplayName" class="mb-1"></h6>
                                    <p id="roleDescription" class="text-muted small mb-2"></p>
                                    <div id="roleTools" class="d-flex flex-wrap gap-1">
                                        <!-- Tools badges will be added here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Configuration -->
                        <div class="mb-3">
                            <label for="temperature" class="form-label">Temperature</label>
                            <input type="range" class="form-range" id="temperature" 
                                   min="0" max="2" step="0.1" value="0.7">
                            <div class="d-flex justify-content-between">
                                <small>0.0</small>
                                <small id="temperatureValue">0.7</small>
                                <small>2.0</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="maxTokens" class="form-label">Max Tokens</label>
                            <input type="number" class="form-control" id="maxTokens" 
                                   min="1" max="4096" value="1000">
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="startNewSession()" disabled id="startBtn">
                            <i class="fas fa-play me-1"></i>
                            Start Session
                        </button>
                        <button class="btn btn-warning" onclick="clearChat()" id="clearBtn">
                            <i class="fas fa-trash me-1"></i>
                            Clear Chat
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Session Info -->
            <div class="card mt-3" id="sessionCard" style="display: none;">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Session Info
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-1"><strong>Session ID:</strong></p>
                    <p class="text-muted small" id="sessionId">-</p>
                    <p class="mb-1"><strong>Messages:</strong></p>
                    <p class="text-muted small" id="messageCount">0</p>
                    <p class="mb-1"><strong>Started:</strong></p>
                    <p class="text-muted small" id="sessionStarted">-</p>
                </div>
            </div>
        </div>
        
        <!-- Chat Area -->
        <div class="col-md-9">
            <div class="card h-100">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Chat with AI Agent
                        </h5>
                        <div id="statusIndicator" class="badge bg-secondary">
                            Not Connected
                        </div>
                    </div>
                </div>
                <div class="card-body d-flex flex-column p-0">
                    <!-- Chat Messages -->
                    <div class="chat-container flex-grow-1 p-3" id="chatContainer">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-robot fa-3x mb-3"></i>
                            <h5>Welcome to AI Agent Testing</h5>
                            <p>Select a role and start a session to begin chatting with the AI agent.</p>
                        </div>
                    </div>
                    
                    <!-- Chat Input -->
                    <div class="border-top p-3">
                        <form id="chatForm" onsubmit="sendMessage(event)">
                            <div class="input-group">
                                <input type="text" class="form-control" id="messageInput" 
                                       placeholder="Type your message..." disabled>
                                <button class="btn btn-primary" type="submit" disabled id="sendBtn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0" id="loadingMessage">Loading...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentSession = null;
let currentRole = null;
let messageCount = 0;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadRoles();
    setupEventListeners();
    
    // Check if role is pre-selected from URL
    const urlParams = new URLSearchParams(window.location.search);
    const selectedRole = urlParams.get('role');
    if (selectedRole) {
        document.getElementById('roleSelect').value = selectedRole;
        loadRole();
    }
});

// Setup event listeners
function setupEventListeners() {
    // Temperature slider
    const temperatureSlider = document.getElementById('temperature');
    temperatureSlider.addEventListener('input', function() {
        document.getElementById('temperatureValue').textContent = this.value;
    });
}

// Load available roles
async function loadRoles() {
    try {
        const response = await utils.apiGet('/roles?active_only=true');
        const roleSelect = document.getElementById('roleSelect');
        
        // Clear existing options except the first one
        roleSelect.innerHTML = '<option value="">Choose a role...</option>';
        
        response.roles.forEach(role => {
            const option = document.createElement('option');
            option.value = role.name;
            option.textContent = role.display_name;
            roleSelect.appendChild(option);
        });
        
    } catch (error) {
        console.error('Failed to load roles:', error);
        utils.showAlert('Failed to load roles: ' + error.message, 'danger');
    }
}

// Load selected role
async function loadRole() {
    const roleName = document.getElementById('roleSelect').value;
    if (!roleName) {
        document.getElementById('roleInfo').style.display = 'none';
        document.getElementById('startBtn').disabled = true;
        return;
    }
    
    try {
        const response = await utils.apiGet(`/roles/${roleName}`);
        currentRole = response;
        
        // Update role info
        document.getElementById('roleDisplayName').textContent = response.display_name;
        document.getElementById('roleDescription').textContent = response.description || 'No description';
        
        // Update tools
        const toolsContainer = document.getElementById('roleTools');
        toolsContainer.innerHTML = response.tools.map(tool => 
            `<span class="badge bg-secondary">${utils.escapeHtml(tool)}</span>`
        ).join('');
        
        // Update configuration
        if (response.config) {
            document.getElementById('temperature').value = response.config.temperature || 0.7;
            document.getElementById('temperatureValue').textContent = response.config.temperature || 0.7;
            document.getElementById('maxTokens').value = response.config.max_tokens || 1000;
        }
        
        document.getElementById('roleInfo').style.display = 'block';
        document.getElementById('startBtn').disabled = false;
        
    } catch (error) {
        console.error('Failed to load role:', error);
        utils.showAlert('Failed to load role: ' + error.message, 'danger');
    }
}

// Start new session
async function startNewSession() {
    if (!currentRole) return;
    
    try {
        utils.showLoading(true, 'Starting session...');
        
        const sessionData = {
            name: `Session - ${currentRole.display_name} - ${new Date().toLocaleString()}`,
            role_name: currentRole.name,
            config: {
                temperature: parseFloat(document.getElementById('temperature').value),
                max_tokens: parseInt(document.getElementById('maxTokens').value)
            }
        };
        
        const response = await utils.apiPost('/sessions', sessionData);
        currentSession = response;
        messageCount = 0;
        
        // Update UI
        document.getElementById('sessionId').textContent = response.id;
        document.getElementById('messageCount').textContent = '0';
        document.getElementById('sessionStarted').textContent = utils.formatDate(response.created_at);
        document.getElementById('sessionCard').style.display = 'block';
        document.getElementById('statusIndicator').textContent = 'Connected';
        document.getElementById('statusIndicator').className = 'badge bg-success';
        
        // Enable chat
        document.getElementById('messageInput').disabled = false;
        document.getElementById('sendBtn').disabled = false;
        
        // Clear chat and add welcome message
        clearChatMessages();
        addMessage('system', `Session started with role: ${currentRole.display_name}`);
        
        utils.showAlert('Session started successfully', 'success', 2000);
        
    } catch (error) {
        console.error('Failed to start session:', error);
        utils.showAlert('Failed to start session: ' + error.message, 'danger');
    } finally {
        utils.showLoading(false);
    }
}

// Send message
async function sendMessage(event) {
    event.preventDefault();
    
    const input = document.getElementById('messageInput');
    const message = input.value.trim();
    
    if (!message || !currentSession) return;
    
    // Add user message to chat
    addMessage('user', message);
    input.value = '';
    
    // Disable input while processing
    input.disabled = true;
    document.getElementById('sendBtn').disabled = true;
    
    try {
        // Send message to API
        const response = await utils.apiPost(`/sessions/${currentSession.id}/messages`, {
            content: message
        });
        
        // Add assistant response
        addMessage('assistant', response.content);
        messageCount += 2;
        document.getElementById('messageCount').textContent = messageCount.toString();
        
    } catch (error) {
        console.error('Failed to send message:', error);
        addMessage('system', 'Error: Failed to send message. Please try again.');
        utils.showAlert('Failed to send message: ' + error.message, 'danger');
    } finally {
        // Re-enable input
        input.disabled = false;
        document.getElementById('sendBtn').disabled = false;
        input.focus();
    }
}

// Add message to chat
function addMessage(type, content) {
    const container = document.getElementById('chatContainer');
    
    // Remove welcome message if it exists
    const welcomeMessage = container.querySelector('.text-center.text-muted');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.innerHTML = `
        <div>${utils.escapeHtml(content)}</div>
        <div class="message-time">${new Date().toLocaleTimeString()}</div>
    `;
    
    container.appendChild(messageDiv);
    container.scrollTop = container.scrollHeight;
}

// Clear chat messages
function clearChatMessages() {
    const container = document.getElementById('chatContainer');
    container.innerHTML = '';
}

// Clear chat
function clearChat() {
    const confirmed = confirm('Are you sure you want to clear the chat history?');
    if (confirmed) {
        clearChatMessages();
        messageCount = 0;
        document.getElementById('messageCount').textContent = '0';
    }
}
</script>
{% endblock %}
